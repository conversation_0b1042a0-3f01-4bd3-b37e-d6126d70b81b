using System.Net;
using System.Net.Sockets;

public class DnsServer
{
    private const int MaxUdpSize = 512; // As per DNS standards for non-EDNS
    private readonly IPEndPoint _listenEndPoint = new(IPAddress.Any, 2053);
    private readonly IPEndPoint? _resolverEndPoint;

    public DnsServer(IPEndPoint? resolverEndPoint)
    {
        _resolverEndPoint = resolverEndPoint;
    }

    public async Task StartAsync(CancellationToken token)
    {
        // Create and configure the main listening socket
        using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        socket.Bind(_listenEndPoint);

        Console.WriteLine($"DNS Server listening on {_listenEndPoint}");

        // A single buffer is allocated once and reused for all receive operations
        var buffer = new byte[MaxUdpSize];
        
        // The remote endpoint for the received packet will be stored here
        var remoteEndPoint = new IPEndPoint(IPAddress.Any, 0);

        while (!token.IsCancellationRequested)
        {
            // Asynchronously wait for an incoming packet
            var result = await socket.ReceiveFromAsync(buffer, SocketFlags.None, remoteEndPoint, token);
            var receivedBytes = buffer.AsMemory(0, result.ReceivedBytes);
            
            // Offload processing to a separate task to keep the receive loop available
            _ = ProcessPacketAsync(socket, receivedBytes, (IPEndPoint)result.RemoteEndPoint, token);
        }
    }

    private async Task ProcessPacketAsync(Socket socket, ReadOnlyMemory<byte> queryBytes, IPEndPoint remoteEp, CancellationToken token)
    {
        try
        {
            // For Stage 1, we simply log the request and send a dummy response.
            // The content of queryBytes is ignored.
            Console.WriteLine($"Received {queryBytes.Length} bytes from {remoteEp}");

            // A hardcoded, single-byte response is sufficient for this stage.
            var responseBytes = new byte[] { 0x00 };

            // Send the response back to the client
            await socket.SendToAsync(responseBytes, SocketFlags.None, remoteEp, token);
        }
        catch (Exception ex)
        {
            // Log any errors during processing, but don't let them crash the server
            Console.WriteLine($"Error processing packet from {remoteEp}: {ex.Message}");
        }
    }
}
